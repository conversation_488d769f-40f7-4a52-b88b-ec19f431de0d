/*
  SerialPassthrough sketch
*/
#include <Arduino.h>
#include <UART.h>

#define DEBUG_TERMINAL

// #define BOARD_IBUS_SERIAL_PORT
#define BOARD_NRF_SERIAL_PORT


// ##################################### //
// ##################################### //

#if defined(BOARD_IBUS_SERIAL_PORT) 
    #define NORM_SERIAL
#elif defined(BOARD_NRF_SERIAL_PORT)
    #define SOFT_SERIAL
    #include <SoftwareSerial.h>
#else
    #error "Not supported"
#endif

#if defined( MEGACOREX_DEFAULT_48PIN_PINOUT )
    HardwareSerial & DCC_EX_Serial = Serial;
    HardwareSerial & iBUS_Serial = Serial1;
    HardwareSerial & Terminal = Serial2;
    #if defined (NORM_SERIAL)        
        HardwareSerial & ESP32_Serial = Serial3;
    #elif defined (SOFT_SERIAL)
        SoftwareSerial ESP32_Serial(PIN_PA5, PIN_PA4); // RX, TX
    #endif  
#elif defined ( NANO_EVERY_PINOUT )
    HardwareSerial & DCC_EX_Serial = Serial3;
    HardwareSerial & iBUS_Serial = Serial1;
    HardwareSerial & Terminal = Serial;
    #if defined (NORM_SERIAL)        
        HardwareSerial & ESP32_Serial = Serial2;
    #elif defined (SOFT_SERIAL)
        SoftwareSerial ESP32_Serial(PIN_PA5, PIN_PA4); // RX, TX
    #endif  
#endif

#define ON HIGH
#define OFF LOW

// ##################################### //

const int ledPin =  LED_BUILTIN;// the number of the LED pin
bool ledState = LOW;             // ledState used to set the LED

void LED(bool state){
    if(ledState != state) {    
        digitalWrite(ledPin, state);
        ledState = state;
    }
}

// ##################################### //

const byte numChars = 32;
char DCC_EX_Data[numChars];   // an array to store the received data
boolean newDCC_EX_Data = false;
char ESP32_Data[numChars];   // an array to store the received data
boolean newESP32_Data = false;

void recvWithEndMarker(HardwareSerial &serialPort, char* buffer, boolean &dataFlag) {
    static byte ndx = 0;
    char endMarker = '>';
    char rc;
    
    while (serialPort.available() > 0 && dataFlag == false) {
        rc = serialPort.read();
  
        if (rc != endMarker) {
            buffer[ndx] = rc;
            ndx++;
            if (ndx >= numChars) {
                ndx = numChars - 1;
            }
        }
        else {
            buffer[ndx] = '\0';
            ndx = 0;
            dataFlag = true;
        }
    }
}
    
void sendNewData(HardwareSerial &outputSerial, char* buffer, boolean &dataFlag) {
    if (dataFlag == true) {    
        outputSerial.println(buffer);
    }
}
#if defined (SOFT_SERIAL)
    void recvWithEndMarker(SoftwareSerial &serialPort, char* buffer, boolean &dataFlag) {
        static byte ndx = 0;
        char endMarker = '\n';
        char rc;        
        
        while (serialPort.available() > 0 && dataFlag == false) {
            LED(ON);
            rc = serialPort.read();
    
            if (rc != endMarker) {
                buffer[ndx] = rc;
                ndx++;
                if (ndx >= numChars) {
                    ndx = numChars - 1;
                }
            }
            else {
                buffer[ndx] = '\0';
                ndx = 0;
                dataFlag = true;
            }
        }
    }
        
    void sendNewData(SoftwareSerial &outputSerial, char* buffer, boolean &dataFlag) {
        if (dataFlag == true) {    
            outputSerial.println(buffer);
        }
    }
#endif
  
void sendComplete(boolean &dataFlag){
    dataFlag = false;
    LED(OFF);
}

// ##################################### //

void setup() {

    // set the digital pin as output:
    pinMode(ledPin, OUTPUT); 
    LED(ON);

    DCC_EX_Serial.begin(115200);    
    ESP32_Serial.begin(9600);           

    #if defined (DEBUG_TERMINAL)
        Terminal.begin(115200);
        Terminal.println("WiDCC_4809_UART_Passthrough");
    #endif  

    LED(OFF);
}

void loop() {

    recvWithEndMarker(ESP32_Serial, ESP32_Data, newESP32_Data);
    #if defined (DEBUG_TERMINAL)        
        sendNewData(Terminal, ESP32_Data, newESP32_Data);
    #endif
    sendNewData(DCC_EX_Serial, ESP32_Data, newESP32_Data);
    sendComplete(newESP32_Data);

}